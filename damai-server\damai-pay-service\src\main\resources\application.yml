#服务端口
server:
  port: 6087
# 应用名称
spring:
  profiles:
    active: local
  application:
    name: ${prefix.distinction.name:damai}-pay-service
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    servlet:
      load-on-startup: 1
    jackson:
      time-zone: GMT+8
      date-format: yyyy-MM-dd HH:mm:ss
      generator:
        WRITE_NUMBERS_AS_STRINGS: true
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:shardingsphere-pay-${spring.profiles.active}.yaml
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      timeout: 3000
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  global-config:
    db-config:
      logic-delete-field: status
      logic-delete-value: 0
      logic-not-delete-value: 1
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    local-cache-scope: statement
feign:
  sentinel:
    enabled: false
  hystrix:
    enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 512
    response:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
    health:
      show-details: always
  security:
    enabled: false
  health:
    elasticsearch:
      enabled: false
jasypt:
  encryptor:
    password: bgtjkjl!%^sdc
    algorithm: PBEWithMD5AndDES
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      #生成文档所需的扫包路径，一般为启动类目录
      packages-to-scan: com.damai.controller
#knife4j配置
knife4j:
  #是否启用增强设置
  enable: true
  #开启生产环境屏蔽
  production: false
  #是否启用登录认证
  basic:
    enable: false
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
alipay:
  appId: 9021000134628973
  sellerId: 2088721028872203
  gatewayUrl: https://openapi-sandbox.dl.alipaydev.com/gateway.do
  merchantPrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCOS28T1EpfBQmSW4y/9TNWbD1jPgQiA7NUBbp3+atkBRYRHROWAuDleYNfUu3pREnoMV60leTvxowNBVIKj8uAgk4nBplhU0EegGOF2HRI0Q5maMm5ufbrfyGgnxcOb3CBUJUzfJt60ueeoUL4l56lvQV91MdqbFHEe6yOcTT3FmUFm3FQ9iFS2Nt+iHEW7grYKU1y/mehNszowBSzWhiTV03Jn9PnXjRACV3PvMJ0RhpQzrZ+8LsRlfQPsp3xwDFwx49u95Q8JlQlr3TzUbtE4EmPCUWMQSwmdlR7yiCNqvMLp4Y0sVtQKVnKvPLlP2UbVduMwY6pdeBy5bKr4yTnAgMBAAECggEABNOcNfD6gNSb6YH/eTdpAWJ0hpxA6aJ0f8Id/BnU7XSatgPleS4p0L7ZbO9UMJhnZV/fVYLMHDIA6CQpD0CnZ3mECyDXLQz05YL3XT/lWd71fRXK2ejr7jsGufsLfirbhxdjqDZu07C5uJDBw8roz7hkqksAZKS99Us4pDBE2qizlXVLZbBN0SNYNUTbkLKTy+lHamT+wTXPnb78Fl/nC8GAQ+cFCQ8y3xjfs0S6+Xbvr57ctqKr/QvNFEv4uYnn7IQTEpv7y6YX//P5w3GP6+x9ppCfwqW2CrkvUzVjnugCLtPQ6graaULg3JWsZgayQDoBEuG4Bp3M0tYlsHTa+QKBgQDqczjN0/LHeE2RtBZCDRE+2CByrX3eHMThfSxGcUD6aee+jP+S2OUjgjMFoqEbXz3/ZhU1rYCTjDsp8wgL27Lh2TYf7Rm9LHC1YIeixh8oNWpo1Bt5UWeFXBRTwNuiJUDx9ZOBygQ5f6hiL2Dnru0pVIh3H1OR7LuSWehmOaa7NQKBgQCbX7qjJRDENdjds0qWgM5YUtNiAa7t6bhq1n3UUf3Gy4SEIpEs5P0L3urdvrxFv/BdakIox1/1ze3y+36TwTjJuli1j5Bn+X8TMPKmXhpZTzYg3iLZU6ufmcJizQtrJGW7qfF/xN+FEaacO16xxIhTn55fX6JlQ3wRc0crq95HKwKBgDk3aXOB+EwJ5hRa+NuhyMWnhTO3d3JPXxUQsU93sD3svqvYST8TQ8iIq98CM4M0mTLNPPvenfRkUsQIFbyfH2bS1cyw1ok+YTtClZd59nyfnJp8uT/ASB1ZvXQ7Kt7FbLYgEqGoJaPKrdNYcLPaEvL1OGCp+oEwXw52dqsMAs7JAoGANg+Z7fgQnGSr/3/JvRI2Oc8f06MITambzDizcXPR3CCmDB/G6ZsHXOEEUYFzGsXNCYPWXz/36JvtZjURdrGtY6teAFZihXrvNys50p89hcOk7sy7zWhqd87VLvFvPqlcaG6aI73JlBBvKz8RFMNwN8B7J24sIBXaDmxQ4CYd4h8CgYB4jsYgvI7i16aDr3YpiapvIIoALEU8W9V0+TgyowBzDmTQW4rKCSQ26bgdFTWZH3lGVpAoOt5XQvpkPCstZErCjdzBwV97ek9buuKY9ea7Ww7ICQSSusiN8Oyjp4Rf1RmnsAU5osS6YCH9u/UC5fgfQVOM3sSlEdsG2CBYMBk19w==
  alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApcNgbnwGS3gDGv9ohSJaykXmJvfq1m0xrlawaJf5oJaYFC5lkGyYaNG115XBInt+NDL8HnBXdMiHs5PfSZXcSW0zmEF1/pf/d3nwdGr5z+NMRND1eRwKJ+LhB/eZMqaS2CeswQxh83etI+72i3TwFsrfNcE2ylWDvBfl1g9VZ/yCcRbpPejEcdok2cx7bcqQwRs18OrGeL1ix+R9DKLYtOIiJlzAZpGsqUSNlZgN2rT62ZQmjKCH/vzF1DLgqV8s20sT0Xx1h4YPDPNoAH0wgxExcOQbuZsc3EIiJhfVlhnQXgxlu+ZIC+2oOBqvf3XlarEagq/PSt9iacjcGzpprQIDAQAB
  contentKey: 43qu8K/LCThAv0EGu7CwFw==