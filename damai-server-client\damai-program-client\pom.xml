<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai-server-client</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>damai-program-client</artifactId>

    <name>program-client</name>
    <description>rpc</description>
    
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-user-client</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

</project>
