<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.damai.mapper.TicketCategoryMapper">
    <select id="selectAggregateList" resultType="com.damai.entity.TicketCategoryAggregate">
        select
            program_id,min(price) as min_price,max(price) as max_price
        from d_ticket_category
        where program_id in
        <foreach collection='programIdList' item='programId' index='index' open='(' close=')' separator=','>
            #{programId,jdbcType=BIGINT}
        </foreach>
        group by program_id
    </select>
    
    <update id="reduceRemainNumber">
        update
            d_ticket_category
        set remain_number = remain_number - #{amount,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
        and program_id = #{programId,jdbcType=BIGINT}
        and <![CDATA[remain_number >= #{amount,jdbcType=BIGINT}]]>
    </update>

    <update id="increaseRemainNumber">
        update
            d_ticket_category
        set remain_number = remain_number + #{amount,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
          and program_id = #{programId,jdbcType=BIGINT}
          and <![CDATA[remain_number + #{amount,jdbcType=BIGINT} <= total_number]]>
    </update>
    
    <update id="batchUpdateRemainNumber">
        <foreach collection="ticketCategoryCountDtoList" item="ticketCategoryCountDto" index="index">
            update
                d_ticket_category
            set remain_number = remain_number - #{ticketCategoryCountDto.count,jdbcType=BIGINT}
            where id = #{ticketCategoryCountDto.ticketCategoryId,jdbcType=BIGINT}
            and program_id = #{programId,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>