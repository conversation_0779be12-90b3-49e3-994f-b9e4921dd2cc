package com.damai.controller;

import com.alibaba.fastjson2.JSON;
import com.damai.common.ApiResponse;
import com.damai.context.DelayQueueContext;
import com.damai.domain.ReconciliationTaskData;
import com.damai.dto.*;
import com.damai.scheduletask.OrderDataTask;
import com.damai.scheduletask.ReconciliationTask;
import com.damai.service.OrderService;
import com.damai.service.OrderTaskService;
import com.damai.vo.AccountOrderCountVo;
import com.damai.vo.OrderGetVo;
import com.damai.vo.OrderListVo;
import com.damai.vo.OrderPayCheckVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @program: 极度真实还原大麦网高并发实战项目。 添加 阿星不是程序员 微信，添加时备注 大麦 来获取项目的完整资料 
 * @description: 订单 控制层
 * @author: 阿星不是程序员
 **/
@RestController
@RequestMapping("/order")
@Tag(name = "order", description = "订单")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private OrderTaskService orderTaskService;
    
    @Autowired
    private ReconciliationTask reconciliationTask;
    
    @Autowired
    private OrderDataTask orderDataTask;

    @Autowired
    private DelayQueueContext delayQueueContext;
    
    @Operation(summary  = "订单创建(不提供给前端调用，只允许内部program服务调用)")
    @PostMapping(value = "/create")
    public ApiResponse<String> create(@Valid @RequestBody OrderCreateDto orderCreateDto) {
        return ApiResponse.ok(orderService.create(orderCreateDto));
    }
    
    @Operation(summary  = "订单支付")
    @PostMapping(value = "/pay")
    public ApiResponse<String> pay(@Valid @RequestBody OrderPayDto orderPayDto) {
        return ApiResponse.ok(orderService.pay(orderPayDto));
    }
    
    @Operation(summary  = "订单支付后状态检查")
    @PostMapping(value = "/pay/check")
    public ApiResponse<OrderPayCheckVo> payCheck(@Valid @RequestBody OrderPayCheckDto orderPayCheckDto) {
        return ApiResponse.ok(orderService.payCheck(orderPayCheckDto));
    }
    
    @Operation(summary  = "支付宝支付后回调通知")
    @PostMapping(value = "/alipay/notify")
    public String alipayNotify(HttpServletRequest request) {
        return orderService.alipayNotify(request);
    }
    
    @Operation(summary  = "查看订单列表")
    @PostMapping(value = "/select/list")
    public ApiResponse<List<OrderListVo>> selectList(@Valid @RequestBody OrderListDto orderListDto) {
        return ApiResponse.ok(orderService.selectList(orderListDto));
    }
    
    @Operation(summary  = "查看订单详情")
    @PostMapping(value = "/get")
    public ApiResponse<OrderGetVo> get(@Valid @RequestBody OrderGetDto orderGetDto) {
        return ApiResponse.ok(orderService.get(orderGetDto));
    }
    
    @Operation(summary  = "账户下某个节目的订单数量(不提供给前端调用，只允许内部program服务调用)")
    @PostMapping(value = "/account/order/count")
    public ApiResponse<AccountOrderCountVo> accountOrderCount(@Valid @RequestBody AccountOrderCountDto accountOrderCountDto) {
        return ApiResponse.ok(orderService.accountOrderCount(accountOrderCountDto));
    }
    
    @Operation(summary  = "查看缓存中的订单")
    @PostMapping(value = "/get/cache")
    public ApiResponse<String> getCache(@Valid @RequestBody OrderGetDto orderGetDto) {
        return ApiResponse.ok(orderService.getCache(orderGetDto));
    }
    
    @Operation(summary  = "订单详情取消")
    @PostMapping(value = "/cancel")
    public ApiResponse<Boolean> cancel(@Valid @RequestBody OrderCancelDto orderCancelDto) {
        return ApiResponse.ok(orderService.initiateCancel(orderCancelDto));
    }

    @Operation(summary  = "对账任务执行")
    @PostMapping(value = "/reconciliation/task")
    public ApiResponse<ReconciliationTaskData> reconciliationTask(@Valid @RequestBody ProgramGetDto programGetDto) {
        return ApiResponse.ok(orderTaskService.reconciliationTask(programGetDto.getId()));
    }
    
    @Operation(summary  = "对账任务执行(全部)")
    @PostMapping(value = "/reconciliation/task/all")
    public ApiResponse<ReconciliationTaskData> reconciliationTaskAll() {
        reconciliationTask.reconciliationTask();
        return ApiResponse.ok();
    }
    
    @Operation(summary  = "测试")
    @PostMapping(value = "/test")
    public ApiResponse<Void> test() {
        orderDataTask.executeTask();
        return ApiResponse.ok();
    }

    @Operation(summary  = "测试")
    @PostMapping(value = "/testDelayQueue")
    public ApiResponse<Void> testDelayQueue() {
        TestSendDto testSendDto = new TestSendDto();
        testSendDto.setCount(1L);
        testSendDto.setMessage("测试测试");
        testSendDto.setTime(System.currentTimeMillis());
        delayQueueContext.sendMessage("test-topic", JSON.toJSONString(testSendDto), 10, TimeUnit.SECONDS);
        return ApiResponse.ok();
    }
}
