<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai-service-gray-transition-framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>damai-service-gray-transition-gateway-framework</artifactId>
    <name>damai-service-gray-transition-gateway-framework</name>
    <description>灰度服务gateway组件</description>

    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-gray-transition-base-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
    </dependencies>
</project>
