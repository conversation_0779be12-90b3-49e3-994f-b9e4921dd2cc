<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai-redisson-framework</artifactId>
        <version>${revision}</version>
    </parent>
    
    <artifactId>damai-service-delay-queue-framework</artifactId>
    <name>service-delay-queue-framework</name>
    <description>延迟队列</description>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>org.redisson</groupId>-->
<!--            <artifactId>redisson-spring-boot-starter</artifactId>-->
<!--            <version>${redisson.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.redisson</groupId>-->
<!--                    <artifactId>redisson-spring-data-25</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.redisson</groupId>-->
<!--            <artifactId>redisson-spring-data-23</artifactId>-->
<!--            <version>${redisson.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-redisson-common-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-common</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
