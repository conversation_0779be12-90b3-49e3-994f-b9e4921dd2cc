<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.example</groupId>
	<artifactId>damai_pro</artifactId>
	<version>${revision}</version>
	<packaging>pom</packaging>

	<name>${project.artifactId}</name>
	<description>
		诚意推荐!!   真实还原生产环境的微服务企业级高并发项目!!
		SpringBoot + SpringCloudAlibaba + 多线程 + Redis + Kafka + ELK + Docker + Sentinel 等技术
		包括完整的各种组件配置、高并发下的支付订单解决方案、使用设计模式进行解耦、完整的配套文档和视频
	</description>

	<modules>
		<module>damai-common</module>
		<module>damai-redis-tool-framework</module>
		<module>damai-elasticsearch-framework</module>
		<module>damai-id-generator-framework</module>
		<module>damai-spring-cloud-framework</module>
		<module>damai-thread-pool-framework</module>
		<module>damai-redisson-framework</module>
		<module>damai-server</module>
		<module>damai-server-client</module>
		<module>damai-captcha-manage-framework</module>
	</modules>

	<properties>
		<revision>0.0.1-SNAPSHOT</revision>
		<java.version>17</java.version>
		<project.sourceEncoding>UTF-8</project.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<spring-boot.version>3.3.0</spring-boot.version>
		<spring-cloud.version>2023.0.2</spring-cloud.version>
		<spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
		<fastjson.version>1.2.83</fastjson.version>
		<oshi-core.version>6.2.2</oshi-core.version>
		<maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
		<jdk.version>17</jdk.version>
		<nacos.version>2.0.3</nacos.version>
		<jasypt.version>3.0.5</jasypt.version>
		<maven-compiler.version>3.8.1</maven-compiler.version>
		<maven-resources.version>3.2.0</maven-resources.version>
		<maven-source.version>3.2.1</maven-source.version>
		<flatten-maven.version>1.4.1</flatten-maven.version>
		<fastjson.version>2.0.9</fastjson.version>
		<oshi-core.version>6.2.2</oshi-core.version>
		<commons-lang.version>2.6</commons-lang.version>
		<jjwt.version>0.9.1</jjwt.version>
		<hutool.version>5.8.25</hutool.version>
		<log4j.version>2.17.0</log4j.version>
		<pagehelper.version>5.2.0</pagehelper.version>
		<commons-collections.version>3.2.2</commons-collections.version>
		<spring-boot-admin-starter-server.version>3.3.0</spring-boot-admin-starter-server.version>
		<mybatis-plus.version>3.5.7</mybatis-plus.version>
		<mybatis-plus-generator.version>3.5.7</mybatis-plus-generator.version>
		<freemarker.version>2.3.29</freemarker.version>
		<druid.version>1.1.10</druid.version>
		<spring-boot-admin-starter-client.version>2.3.1</spring-boot-admin-starter-client.version>
		<knife4j.version>4.3.0</knife4j.version>
		<openapi.swagger.version>2.2.0</openapi.swagger.version>
		<redisson.version>3.32.0</redisson.version>
		<qps-helper.version>1.1.3-RELEASE</qps-helper.version>
		<alipay-sdk-java-version>4.38.197.ALL</alipay-sdk-java-version>
		<shardingsphere.version>5.3.2</shardingsphere.version>
		<snakeyaml.version>1.33</snakeyaml.version>
		<spring-boot-starter-captcha.version>1.3.0</spring-boot-starter-captcha.version>
		<caffeine.version>2.9.3</caffeine.version>
		<jaxb.version>2.3.0</jaxb.version>
		<activation.version>1.1.1</activation.version>
		<spotless-maven-plugin.version>2.22.1</spotless-maven-plugin.version>
		<opencsv.version>5.9</opencsv.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler.version}</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.sourceEncoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>${maven-resources.version}</version>
				<configuration>
					<encoding>${project.sourceEncoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>${maven-source.version}</version>
				<configuration>
					<!--install、deploy时会上传源码jar包到仓库，默认是true-->
					<attach>true</attach>
				</configuration>
				<executions>
					<execution>
						<id>attach-source</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<version>${flatten-maven.version}</version>
				<configuration>
					<updatePomFile>true</updatePomFile>
				</configuration>
				<executions>
					<!-- enable flattening -->
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
					</execution>
					<!-- ensure proper cleanup -->
					<execution>
						<id>flatten.clean</id>
						<phase>clean</phase>
						<goals>
							<goal>clean</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
